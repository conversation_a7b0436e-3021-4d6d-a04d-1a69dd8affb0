package com.fj.towercontrol.util

import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import java.util.BitSet

/**
 * 将 BitSet 与 IntArray 互转的 Gson 转换器
 *
 * <AUTHOR>
 * @since 2025/8/7
 */
class GsonBitSetAdapter : TypeAdapter<BitSet>() {

	override fun write(out: JsonWriter, value: BitSet?) {
		if (value == null) {
			out.nullValue()
			return
		}

		out.beginArray()
		var i = value.nextSetBit(0)
		while (i >= 0) {
			out.value(i)
			i = value.nextSetBit(i + 1)
		}
		out.endArray()
	}

	override fun read(reader: JsonReader): BitSet? {
		if (reader.peek() == JsonToken.NULL) {
			reader.nextNull()
			return null
		}

		val bitSet = BitSet()
		reader.beginArray()
		while (reader.hasNext()) {
			val index = reader.nextInt()
			if (index >= 0) {
				bitSet.set(index)
			}
		}
		reader.endArray()
		return bitSet
	}
}
