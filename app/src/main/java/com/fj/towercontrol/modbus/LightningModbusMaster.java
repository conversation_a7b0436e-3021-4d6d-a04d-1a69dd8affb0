package com.fj.towercontrol.modbus;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.fj.towercontrol.util.CustomModbusReq;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import java.io.EOFException;

import okio.Buffer;

/**
 * 雷电预警模块modbus通信
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
public class LightningModbusMaster {
    private static final String TAG = "LightningModbusMaster";
    /**
     * 尝试重连消息
     */
    private static final int MSG_RETRY_CONNECT = 10001;
    /**
     * 重置告警状态消息
     */
    private static final int MSG_READ_NEXT_DATA = 10002;
    private static final int SLAVE_ID = 0x01;
    private static final int START_ADDRESS = 0x00;
    private static final int READ_REGISTER_COUNT = 5;
    private static final long READ_REGISTER_INTERVAL = 10_000L;
    private final Handler handler;
    private final CustomModbusReq modbusReq;

    private LightningModbusMaster() {
        modbusReq = new CustomModbusReq();
        handler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
                if (msg.what == MSG_RETRY_CONNECT) {
                    initModbus();
                } else if (msg.what == MSG_READ_NEXT_DATA) {
                    readLightningData();
                }
            }
        };
    }

    private static class SingletonHolder {
        private static final LightningModbusMaster INSTANCE = new LightningModbusMaster();
    }

    public static LightningModbusMaster getInstance() {
        return LightningModbusMaster.SingletonHolder.INSTANCE;
    }

    public void start() {
        handler.postDelayed(this::initModbus, 2_000);
    }

    public void stop() {
        modbusReq.destroy();
        handler.removeCallbacksAndMessages(null);
    }

    private void initModbus() {
        modbusReq.destroy();
        ModbusParam modbusParam = new ModbusParam()
                .setHost("*************")
                .setPort(51001)
                .setEncapsulated(true)
                .setKeepAlive(true)
                .setTimeout(800)
                .setRetries(0);
        modbusReq.setParam(modbusParam)
                .init(new OnRequestBack<>() {
                    @Override
                    public void onSuccess(String s) {
                        Log.d(TAG, "initModbus onSuccess: " + s);
                        readLightningData();
                    }

                    @Override
                    public void onFailed(String msg) {
                        Logger.e(TAG, "initModbus onFailed: " + msg);
                        handler.sendEmptyMessageDelayed(MSG_RETRY_CONNECT, 2_000);
                    }
                });
    }

    private void readLightningData() {
        modbusReq.readHoldingRegistersRaw(new OnRequestBack<>() {
            @Override
            public void onSuccess(byte[] data) {
                if (data == null) {
                    handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
                    return;
                }
                Log.d(TAG, "readLightningData onSuccess: " + DataUtil.byte2hex(data));
                try (Buffer buffer = new Buffer().write(data)) {
                    buffer.readInt();
                } catch (EOFException e) {
                    throw new RuntimeException(e);
                }

                handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
            }

            @Override
            public void onFailed(String s) {
                handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
            }
        }, SLAVE_ID, START_ADDRESS, READ_REGISTER_COUNT);
    }

}
